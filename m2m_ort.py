import tkinter as tk
from tkinter import ttk, messagebox, Menu
import threading
import time
import os
import subprocess
import ctypes
import serial
import configparser
import sys
from datetime import datetime
import openpyxl

class M2MORT:
    def __init__(self, root):
        self.root = root
        self.root.title("M2M ORT")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)

        # Script könyvtár meghat<PERSON>
        if getattr(sys, 'frozen', False):
            self.script_dir = os.path.dirname(sys.executable)
        else:
            self.script_dir = os.path.dirname(os.path.abspath(__file__))

        self.config_path = os.path.join(self.script_dir, 'settings.cfg')

        # Config betöltése vagy létrehozása
        self.load_config()

        # Modem adatok tárolása
        self.modems = []
        self.modem_widgets = []

        # Ping és debug sz<PERSON>lak
        self.ping_threads = {}
        self.debug_threads = {}
        self.running_pings = {}
        self.running_debugs = {}

        self.setup_ui()
        self.load_excel_data()

    def load_config(self):
        """Config fájl betöltése vagy létrehozása alapértelmezett értékekkel"""
        self.config = configparser.ConfigParser()

        # Alapértelmezett beállítások
        default_config = {
            'PING': {
                'interval_seconds': '5',
                'packet_size_bytes': '32',
                'timeout_ms': '1000',
                'line_break_minutes': '30'
            }
        }

        if os.path.exists(self.config_path):
            self.config.read(self.config_path, encoding='utf-8')
        else:
            # Alapértelmezett config létrehozása
            for section, options in default_config.items():
                self.config.add_section(section)
                for key, value in options.items():
                    self.config.set(section, key, value)
            self.save_config()

        # Ping beállítások betöltése
        self.ping_interval = self.config.getint('PING', 'interval_seconds', fallback=5)
        self.ping_packet_size = self.config.getint('PING', 'packet_size_bytes', fallback=32)
        self.ping_timeout = self.config.getint('PING', 'timeout_ms', fallback=1000)
        self.ping_line_break = self.config.getint('PING', 'line_break_minutes', fallback=30)

    def save_config(self):
        """Config fájl mentése"""
        with open(self.config_path, 'w', encoding='utf-8') as configfile:
            self.config.write(configfile)

    def setup_ui(self):
        # Menüsáv létrehozása
        menubar = Menu(self.root)
        self.root.config(menu=menubar)
        
        # SETTINGS menü
        settings_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="SETTINGS", menu=settings_menu)
        settings_menu.add_command(label="Ping Settings", command=self.open_ping_settings)
        
        # Felső gombok frame
        top_frame = ttk.Frame(self.root)
        top_frame.pack(fill="x", padx=10, pady=5)
        
        # CLOSE the CASE gomb (középen)
        close_case_btn = ttk.Button(top_frame, text="CLOSE the CASE", state="disabled")
        close_case_btn.pack(side="left", expand=True)
        
        # EXIT gomb (jobb oldal)
        exit_btn = ttk.Button(top_frame, text="EXIT", command=self.on_exit)
        exit_btn.pack(side="right")
        
        # Modem felirat
        modem_label = ttk.Label(self.root, text="Modem", font=("Arial", 12, "bold"))
        modem_label.pack(pady=(10, 5))
        
        # Görgethető frame a modemekhez
        self.setup_scrollable_frame()
        
    def setup_scrollable_frame(self):
        # Canvas és scrollbar a görgethető listához
        canvas_frame = ttk.Frame(self.root)
        canvas_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.canvas = tk.Canvas(canvas_frame)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Egér görgetés támogatása
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        
    def _on_mousewheel(self, event):
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
    def open_ping_settings(self):
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Ping Settings")
        settings_window.geometry("300x250")
        settings_window.resizable(False, False)
        
        # Ping Interval
        ttk.Label(settings_window, text="Ping Interval Seconds:").pack(pady=5)
        interval_var = tk.StringVar(value=str(self.ping_interval))
        interval_entry = ttk.Entry(settings_window, textvariable=interval_var)
        interval_entry.pack(pady=5)
        
        # Ping Packet Size
        ttk.Label(settings_window, text="Ping Packet Size (bytes):").pack(pady=5)
        size_var = tk.StringVar(value=str(self.ping_packet_size))
        size_entry = ttk.Entry(settings_window, textvariable=size_var)
        size_entry.pack(pady=5)
        
        # Ping Timeout
        ttk.Label(settings_window, text="Ping Timeout (ms):").pack(pady=5)
        timeout_var = tk.StringVar(value=str(self.ping_timeout))
        timeout_entry = ttk.Entry(settings_window, textvariable=timeout_var)
        timeout_entry.pack(pady=5)
        
        # Ping Line Break
        ttk.Label(settings_window, text="Ping Line Break (minutes):").pack(pady=5)
        linebreak_var = tk.StringVar(value=str(self.ping_line_break))
        linebreak_entry = ttk.Entry(settings_window, textvariable=linebreak_var)
        linebreak_entry.pack(pady=5)
        
        # Gombok
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(pady=10)
        
        def save_settings():
            try:
                self.ping_interval = int(interval_var.get())
                self.ping_packet_size = int(size_var.get())
                self.ping_timeout = int(timeout_var.get())
                self.ping_line_break = int(linebreak_var.get())

                # Config fájlba mentés
                self.config.set('PING', 'interval_seconds', str(self.ping_interval))
                self.config.set('PING', 'packet_size_bytes', str(self.ping_packet_size))
                self.config.set('PING', 'timeout_ms', str(self.ping_timeout))
                self.config.set('PING', 'line_break_minutes', str(self.ping_line_break))
                self.save_config()

                messagebox.showinfo("Siker", "Beállítások mentve!")
                settings_window.destroy()
            except ValueError:
                messagebox.showerror("Hiba", "Kérjük, csak számokat adjon meg!")
        
        ttk.Button(button_frame, text="Save", command=save_settings).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Close", command=settings_window.destroy).pack(side="left", padx=5)
        
    def load_excel_data(self):
        excel_path = os.path.join(self.script_dir, "data.xlsx")
        if not os.path.exists(excel_path):
            messagebox.showerror("Hiba", f"A data.xlsx fájl nem található: {excel_path}")
            return
            
        try:
            workbook = openpyxl.load_workbook(excel_path)
            sheet = workbook.active
            
            # Második sor után kezdjük (3. sortól)
            for row in sheet.iter_rows(min_row=3, values_only=True):
                if any(row):  # Ha van adat a sorban
                    ip = row[0] if row[0] else ""
                    port = row[1] if row[1] else ""
                    baud_rate = row[2] if row[2] else ""
                    data_type = row[3] if row[3] else ""
                    
                    modem_data = {
                        'ip': str(ip).strip() if ip else "",
                        'port': str(port).strip() if port else "",
                        'baud_rate': str(baud_rate).strip() if baud_rate else "",
                        'data_type': str(data_type).strip() if data_type else ""
                    }
                    
                    if any(modem_data.values()):  # Ha van legalább egy adat
                        self.modems.append(modem_data)
                        
            workbook.close()
            self.create_modem_widgets()
            
        except Exception as e:
            messagebox.showerror("Hiba", f"Excel fájl beolvasási hiba: {str(e)}")
            
    def create_modem_widgets(self):
        for i, modem in enumerate(self.modems):
            self.create_modem_row(i, modem)
            
    def create_modem_row(self, index, modem):
        # Modem sor frame
        row_frame = ttk.Frame(self.scrollable_frame)
        row_frame.pack(fill="x", pady=2, padx=5)
        
        # Modem adatok megjelenítése
        info_text = f"IP: {modem['ip'] or 'N/A'} | Port: {modem['port'] or 'N/A'} | Baud: {modem['baud_rate'] or 'N/A'} | Type: {modem['data_type'] or 'N/A'}"
        info_label = ttk.Label(row_frame, text=info_text)
        info_label.pack(side="left", padx=5)
        
        # IP gomb
        ip_status = "IP ON" if modem['ip'] else "IP OFF"
        ip_btn = ttk.Button(row_frame, text=ip_status, width=8)
        ip_btn.pack(side="left", padx=5)
        
        # DEBUG gomb
        has_debug_data = bool(modem['port'] and modem['baud_rate'] and modem['data_type'])
        debug_status = "DEBUG ON" if has_debug_data else "DEBUG OFF"
        debug_btn = ttk.Button(row_frame, text=debug_status, width=10)
        debug_btn.pack(side="left", padx=5)
        
        # SHOW IP LOG gomb
        show_ip_btn = ttk.Button(row_frame, text="SHOW IP LOG", width=12)
        show_ip_btn.pack(side="left", padx=5)
        
        # SHOW DEBUG LOG gomb
        show_debug_btn = ttk.Button(row_frame, text="SHOW DEBUG LOG", width=15)
        show_debug_btn.pack(side="left", padx=5)
        
        # Widget referenciák tárolása
        widget_data = {
            'frame': row_frame,
            'ip_btn': ip_btn,
            'debug_btn': debug_btn,
            'show_ip_btn': show_ip_btn,
            'show_debug_btn': show_debug_btn,
            'modem': modem
        }
        self.modem_widgets.append(widget_data)
        
        # Eseménykezelők beállítása
        self.setup_modem_events(index, widget_data)
        
        # Automatikus indítás, ha van IP
        if modem['ip']:
            self.start_ping(index)

        # Automatikus debug indítás, ha van port, baud, data type
        if modem['port'] and modem['baud_rate'] and modem['data_type']:
            self.start_debug(index)
            
    def setup_modem_events(self, index, widget_data):
        # IP gomb esemény
        widget_data['ip_btn'].config(command=lambda: self.toggle_ping(index))
        
        # DEBUG gomb esemény  
        widget_data['debug_btn'].config(command=lambda: self.toggle_debug(index))
        
        # SHOW IP LOG gomb esemény
        widget_data['show_ip_btn'].config(command=lambda: self.show_ip_log(index))
        
        # SHOW DEBUG LOG gomb esemény
        widget_data['show_debug_btn'].config(command=lambda: self.show_debug_log(index))
        
    def toggle_ping(self, index):
        modem = self.modems[index]
        if not modem['ip']:
            messagebox.showwarning("Figyelem", "Nincs IP cím megadva ehhez a modemhez.")
            return

        if index in self.running_pings:
            self.stop_ping(index)
        else:
            self.start_ping(index)

    def start_ping(self, index):
        modem = self.modems[index]
        if not modem['ip']:
            return

        self.running_pings[index] = True
        self.modem_widgets[index]['ip_btn'].config(text="IP ON")

        # Ping szál indítása
        thread = threading.Thread(target=self.ping_worker, args=(index,))
        thread.daemon = True
        thread.start()
        self.ping_threads[index] = thread

    def stop_ping(self, index):
        if index in self.running_pings:
            self.running_pings[index] = False
            modem = self.modems[index]
            ip_text = "IP OFF" if modem['ip'] else "IP OFF"
            self.modem_widgets[index]['ip_btn'].config(text=ip_text)
            if index in self.ping_threads:
                del self.ping_threads[index]

    def ping_worker(self, index):
        modem = self.modems[index]
        ip_address = modem['ip']

        # Log fájl létrehozása
        current_date = datetime.now().strftime('%Y%m%d')
        log_dir = os.path.join(self.script_dir, current_date)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_file = os.path.join(log_dir, f"ping_{ip_address}_{current_date}.txt")

        last_line_break = time.time()

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n=== Ping started at {datetime.now().strftime('%Y.%m.%d %H:%M:%S')} ===\n")

            while self.running_pings.get(index, False):
                try:
                    # Console ablak elrejtése
                    SW_HIDE = 0
                    HWND = ctypes.windll.kernel32.GetConsoleWindow()
                    ctypes.windll.user32.ShowWindow(HWND, SW_HIDE)

                    # Ping parancs végrehajtása
                    result = subprocess.run(
                        ["ping", "-n", "1", "-l", str(self.ping_packet_size),
                         "-w", str(self.ping_timeout), ip_address],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        creationflags=subprocess.CREATE_NO_WINDOW
                    )

                    # Console ablak visszaállítása
                    SW_SHOW = 5
                    ctypes.windll.user32.ShowWindow(HWND, SW_SHOW)

                    # Eredmény kiértékelése
                    if "Reply from" in result.stdout:
                        f.write("+")
                    else:
                        f.write("-")
                    f.flush()

                    # Line break ellenőrzése
                    current_time = time.time()
                    if current_time - last_line_break >= (self.ping_line_break * 60):
                        f.write(f"\n{datetime.now().strftime('%Y.%m.%d %H:%M:%S')}\n")
                        last_line_break = current_time

                except Exception as e:
                    f.write("-")
                    f.flush()

                time.sleep(self.ping_interval)

    def toggle_debug(self, index):
        modem = self.modems[index]
        if not (modem['port'] and modem['baud_rate'] and modem['data_type']):
            messagebox.showwarning("Figyelem", "Nincs elegendő adat a debug logoláshoz (port, baud rate, data type szükséges).")
            return

        if index in self.running_debugs:
            self.stop_debug(index)
        else:
            self.start_debug(index)

    def start_debug(self, index):
        modem = self.modems[index]
        if not (modem['port'] and modem['baud_rate'] and modem['data_type']):
            return

        self.running_debugs[index] = True
        self.modem_widgets[index]['debug_btn'].config(text="DEBUG ON")

        # Debug szál indítása
        thread = threading.Thread(target=self.debug_worker, args=(index,))
        thread.daemon = True
        thread.start()
        self.debug_threads[index] = thread

    def stop_debug(self, index):
        if index in self.running_debugs:
            self.running_debugs[index] = False
            modem = self.modems[index]
            has_debug_data = bool(modem['port'] and modem['baud_rate'] and modem['data_type'])
            debug_text = "DEBUG OFF" if has_debug_data else "DEBUG OFF"
            self.modem_widgets[index]['debug_btn'].config(text=debug_text)
            if index in self.debug_threads:
                del self.debug_threads[index]

    def debug_worker(self, index):
        modem = self.modems[index]
        port = f"COM{modem['port']}" if modem['port'].isdigit() else modem['port']
        baud_rate = int(modem['baud_rate'])

        # Log fájl létrehozása
        current_date = datetime.now().strftime('%Y%m%d')
        log_dir = os.path.join(self.script_dir, current_date)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_file = os.path.join(log_dir, f"debug_{port}_{current_date}.txt")

        retry_count = 0
        max_retries = 60  # 1 perc újrapróbálkozás

        while self.running_debugs.get(index, False):
            try:
                with serial.Serial(port, baud_rate, timeout=1) as ser:
                    retry_count = 0  # Sikeres kapcsolat esetén nullázzuk

                    with open(log_file, 'a', encoding='utf-8') as f:
                        f.write(f"\n=== Debug started at {datetime.now().strftime('%Y.%m.%d %H:%M:%S')} ===\n")

                        while self.running_debugs.get(index, False):
                            if ser.in_waiting > 0:
                                try:
                                    line_bytes = ser.readline()
                                    try:
                                        line = line_bytes.decode('utf-8').strip()
                                    except UnicodeDecodeError:
                                        try:
                                            line = line_bytes.decode('iso-8859-1').strip()
                                        except UnicodeDecodeError:
                                            continue

                                    if line:
                                        timestamp = datetime.now().strftime('%Y.%m.%d %H:%M:%S')
                                        f.write(f"[{timestamp}] {line}\n")
                                        f.flush()

                                except Exception as e:
                                    continue

                            time.sleep(0.02)

            except serial.SerialException as e:
                retry_count += 1
                if retry_count >= max_retries:
                    break
                time.sleep(1)  # 1 másodperc várakozás újrapróbálkozás előtt
            except Exception as e:
                break

    def show_ip_log(self, index):
        modem = self.modems[index]
        if not modem['ip']:
            messagebox.showwarning("Figyelem", "Nincs IP cím megadva ehhez a modemhez.")
            return

        current_date = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(self.script_dir, current_date, f"ping_{modem['ip']}_{current_date}.txt")

        if os.path.exists(log_file):
            subprocess.Popen(["notepad.exe", log_file])
        else:
            messagebox.showwarning("Figyelem", "IP log fájl nem található.")

    def show_debug_log(self, index):
        modem = self.modems[index]
        if not modem['port']:
            messagebox.showwarning("Figyelem", "Nincs port megadva ehhez a modemhez.")
            return

        port = f"COM{modem['port']}" if modem['port'].isdigit() else modem['port']
        current_date = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(self.script_dir, current_date, f"debug_{port}_{current_date}.txt")

        if os.path.exists(log_file):
            subprocess.Popen(["notepad.exe", log_file])
        else:
            messagebox.showwarning("Figyelem", "Debug log fájl nem található.")

    def on_exit(self):
        # Minden szál leállítása
        for index in list(self.running_pings.keys()):
            self.stop_ping(index)
        for index in list(self.running_debugs.keys()):
            self.stop_debug(index)

        self.root.quit()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = M2MORT(root)
    root.mainloop()
