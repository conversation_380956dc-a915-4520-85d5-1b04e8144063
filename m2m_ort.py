import tkinter as tk
from tkinter import ttk, messagebox, Menu
import threading
import time
import os
import subprocess
import ctypes
import serial
import configparser
import sys
from datetime import datetime
import openpyxl

class M2MORT:
    def __init__(self, root):
        self.root = root
        self.root.title("M2M ORT")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)

        # Script könyvtár meghat<PERSON>
        if getattr(sys, 'frozen', False):
            self.script_dir = os.path.dirname(sys.executable)
        else:
            self.script_dir = os.path.dirname(os.path.abspath(__file__))

        self.config_path = os.path.join(self.script_dir, 'settings.cfg')

        # Config betöltése vagy létrehozása
        self.load_config()

        # Modem adatok tárolása
        self.modems = []
        self.modem_widgets = []

        # Ping és debug sz<PERSON>lak
        self.ping_threads = {}
        self.debug_threads = {}
        self.running_pings = {}
        self.running_debugs = {}

        self.setup_ui()
        self.load_excel_data()

    def load_config(self):
        """Config fájl betöltése vagy létrehozása alapértelmezett értékekkel"""
        self.config = configparser.ConfigParser()

        # Alapértelmezett beállítások
        default_config = {
            'PING': {
                'interval_seconds': '5',
                'packet_size_bytes': '32',
                'timeout_ms': '1000',
                'line_break_minutes': '30'
            }
        }

        if os.path.exists(self.config_path):
            self.config.read(self.config_path, encoding='utf-8')
        else:
            # Alapértelmezett config létrehozása
            for section, options in default_config.items():
                self.config.add_section(section)
                for key, value in options.items():
                    self.config.set(section, key, value)
            self.save_config()

        # Ping beállítások betöltése
        self.ping_interval = self.config.getint('PING', 'interval_seconds', fallback=5)
        self.ping_packet_size = self.config.getint('PING', 'packet_size_bytes', fallback=32)
        self.ping_timeout = self.config.getint('PING', 'timeout_ms', fallback=1000)
        self.ping_line_break = self.config.getint('PING', 'line_break_minutes', fallback=30)

        # Státusz ellenőrzés beállítások betöltése
        self.status_ip_check_minutes = self.config.getint('STATUS', 'ip_check_minutes', fallback=5)
        self.modem_log_check_minutes = self.config.getint('STATUS', 'modem_log_check_minutes', fallback=5)

    def save_config(self):
        """Config fájl mentése"""
        with open(self.config_path, 'w', encoding='utf-8') as configfile:
            self.config.write(configfile)

    def setup_ui(self):
        # Menüsáv létrehozása
        menubar = Menu(self.root)
        self.root.config(menu=menubar)
        
        # SETTINGS menü (bal oldal)
        settings_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="SETTINGS", menu=settings_menu)
        settings_menu.add_command(label="Ping Settings", command=self.open_ping_settings)

        # EXIT menü (jobb oldal)
        menubar.add_cascade(label="EXIT", command=self.on_exit)

        # Felső gombok frame
        top_frame = ttk.Frame(self.root)
        top_frame.pack(fill="x", padx=10, pady=5)

        # OPEN the CASE gomb (bal oldal)
        open_case_btn = ttk.Button(top_frame, text="OPEN the CASE", state="normal")
        open_case_btn.pack(side="left")

        # CLOSE the CASE gomb (jobb oldal, piros színnel)
        style = ttk.Style()
        style.configure("Red.TButton", foreground="red")
        close_case_btn = ttk.Button(top_frame, text="CLOSE the CASE", style="Red.TButton", state="disabled")
        close_case_btn.pack(side="right")
        
        # Modem felirat
        modem_label = ttk.Label(self.root, text="Modem", font=("Arial", 12, "bold"))
        modem_label.pack(pady=(10, 5))
        
        # Görgethető frame a modemekhez
        self.setup_scrollable_frame()
        
    def setup_scrollable_frame(self):
        # Canvas és scrollbar a görgethető listához
        canvas_frame = ttk.Frame(self.root)
        canvas_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.canvas = tk.Canvas(canvas_frame)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Egér görgetés támogatása
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        
    def _on_mousewheel(self, event):
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
    def open_ping_settings(self):
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Ping Settings")
        settings_window.geometry("300x250")
        settings_window.resizable(False, False)
        
        # Ping Interval
        ttk.Label(settings_window, text="Ping Interval Seconds:").pack(pady=5)
        interval_var = tk.StringVar(value=str(self.ping_interval))
        interval_entry = ttk.Entry(settings_window, textvariable=interval_var)
        interval_entry.pack(pady=5)
        
        # Ping Packet Size
        ttk.Label(settings_window, text="Ping Packet Size (bytes):").pack(pady=5)
        size_var = tk.StringVar(value=str(self.ping_packet_size))
        size_entry = ttk.Entry(settings_window, textvariable=size_var)
        size_entry.pack(pady=5)
        
        # Ping Timeout
        ttk.Label(settings_window, text="Ping Timeout (ms):").pack(pady=5)
        timeout_var = tk.StringVar(value=str(self.ping_timeout))
        timeout_entry = ttk.Entry(settings_window, textvariable=timeout_var)
        timeout_entry.pack(pady=5)
        
        # Ping Line Break
        ttk.Label(settings_window, text="Ping Line Break (minutes):").pack(pady=5)
        linebreak_var = tk.StringVar(value=str(self.ping_line_break))
        linebreak_entry = ttk.Entry(settings_window, textvariable=linebreak_var)
        linebreak_entry.pack(pady=5)
        
        # Gombok
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(pady=10)
        
        def save_settings():
            try:
                self.ping_interval = int(interval_var.get())
                self.ping_packet_size = int(size_var.get())
                self.ping_timeout = int(timeout_var.get())
                self.ping_line_break = int(linebreak_var.get())

                # Config fájlba mentés
                self.config.set('PING', 'interval_seconds', str(self.ping_interval))
                self.config.set('PING', 'packet_size_bytes', str(self.ping_packet_size))
                self.config.set('PING', 'timeout_ms', str(self.ping_timeout))
                self.config.set('PING', 'line_break_minutes', str(self.ping_line_break))
                self.save_config()

                messagebox.showinfo("Siker", "Beállítások mentve!")
                settings_window.destroy()
            except ValueError:
                messagebox.showerror("Hiba", "Kérjük, csak számokat adjon meg!")
        
        ttk.Button(button_frame, text="Save", command=save_settings).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Close", command=settings_window.destroy).pack(side="left", padx=5)
        
    def load_excel_data(self):
        excel_path = os.path.join(self.script_dir, "data.xlsx")
        if not os.path.exists(excel_path):
            messagebox.showerror("Hiba", f"A data.xlsx fájl nem található: {excel_path}")
            return
            
        try:
            workbook = openpyxl.load_workbook(excel_path)
            sheet = workbook.active
            
            # Második sor után kezdjük (3. sortól)
            for row in sheet.iter_rows(min_row=3, values_only=True):
                if any(row):  # Ha van adat a sorban
                    ip = row[0] if row[0] else ""
                    port = row[1] if row[1] else ""
                    baud_rate = row[2] if row[2] else ""
                    data_type = row[3] if row[3] else ""
                    
                    modem_data = {
                        'ip': str(ip).strip() if ip else "",
                        'port': str(port).strip() if port else "",
                        'baud_rate': str(baud_rate).strip() if baud_rate else "",
                        'data_type': str(data_type).strip() if data_type else ""
                    }
                    
                    if any(modem_data.values()):  # Ha van legalább egy adat
                        self.modems.append(modem_data)
                        
            workbook.close()
            self.create_modem_widgets()
            
        except Exception as e:
            messagebox.showerror("Hiba", f"Excel fájl beolvasási hiba: {str(e)}")
            
    def create_modem_widgets(self):
        for i, modem in enumerate(self.modems):
            self.create_modem_row(i, modem)
            
    def create_modem_row(self, index, modem):
        # Modem sor frame
        row_frame = ttk.Frame(self.scrollable_frame)
        row_frame.pack(fill="x", pady=2, padx=5)

        # Modem szám és adatok egységes oszlopokkal
        modem_num = f"{index + 1}."
        ip_text = modem['ip'] or 'N/A'
        port_text = modem['port'] or 'N/A'
        baud_text = modem['baud_rate'] or 'N/A'
        type_text = modem['data_type'] or 'N/A'

        # Egységes szélességű oszlopok
        num_label = ttk.Label(row_frame, text=modem_num, width=4, anchor="w")
        num_label.pack(side="left", padx=2)

        ip_label = ttk.Label(row_frame, text=f"IP: {ip_text}", width=18, anchor="w")
        ip_label.pack(side="left", padx=2)

        port_label = ttk.Label(row_frame, text=f"Port: {port_text}", width=10, anchor="w")
        port_label.pack(side="left", padx=2)

        baud_label = ttk.Label(row_frame, text=f"Baud: {baud_text}", width=12, anchor="w")
        baud_label.pack(side="left", padx=2)

        type_label = ttk.Label(row_frame, text=f"Type: {type_text}", width=12, anchor="w")
        type_label.pack(side="left", padx=2)

        # Gombok színezéssel
        style = ttk.Style()
        style.configure("Green.TButton", foreground="white", background="green")
        style.configure("Red.TButton", foreground="white", background="red")

        # IP gomb
        ip_status = "IP ON" if modem['ip'] else "IP OFF"
        ip_style = "Green.TButton" if modem['ip'] else "Red.TButton"
        ip_btn = ttk.Button(row_frame, text=ip_status, width=8, style=ip_style)
        ip_btn.pack(side="left", padx=2)

        # DEBUG gomb
        has_debug_data = bool(modem['port'] and modem['baud_rate'] and modem['data_type'])
        debug_status = "DEBUG ON" if has_debug_data else "DEBUG OFF"
        debug_style = "Green.TButton" if has_debug_data else "Red.TButton"
        debug_btn = ttk.Button(row_frame, text=debug_status, width=10, style=debug_style)
        debug_btn.pack(side="left", padx=2)

        # SHOW IP LOG gomb (nagyobb méret)
        show_ip_btn = ttk.Button(row_frame, text="SHOW IP LOG", width=15)
        show_ip_btn.pack(side="left", padx=2)

        # SHOW DEBUG LOG gomb (nagyobb méret)
        show_debug_btn = ttk.Button(row_frame, text="SHOW DEBUG LOG", width=18)
        show_debug_btn.pack(side="left", padx=2)
        
        # Widget referenciák tárolása
        widget_data = {
            'frame': row_frame,
            'ip_btn': ip_btn,
            'debug_btn': debug_btn,
            'show_ip_btn': show_ip_btn,
            'show_debug_btn': show_debug_btn,
            'modem': modem
        }
        self.modem_widgets.append(widget_data)
        
        # Eseménykezelők beállítása
        self.setup_modem_events(index, widget_data)
        
        # Automatikus indítás, ha van IP
        if modem['ip']:
            self.start_ping(index)

        # Automatikus debug indítás, ha van port, baud, data type
        if modem['port'] and modem['baud_rate'] and modem['data_type']:
            self.start_debug(index)
            
    def setup_modem_events(self, index, widget_data):
        # IP gomb esemény
        widget_data['ip_btn'].config(command=lambda: self.toggle_ping(index))
        
        # DEBUG gomb esemény  
        widget_data['debug_btn'].config(command=lambda: self.toggle_debug(index))
        
        # SHOW IP LOG gomb esemény
        widget_data['show_ip_btn'].config(command=lambda: self.show_ip_log(index))
        
        # SHOW DEBUG LOG gomb esemény
        widget_data['show_debug_btn'].config(command=lambda: self.show_debug_log(index))
        
    def toggle_ping(self, index):
        modem = self.modems[index]
        if not modem['ip']:
            messagebox.showwarning("Figyelem", "Nincs IP cím megadva ehhez a modemhez.")
            return

        if index in self.running_pings:
            self.stop_ping(index)
        else:
            self.start_ping(index)

    def start_ping(self, index):
        modem = self.modems[index]
        if not modem['ip']:
            return

        self.running_pings[index] = True
        self.modem_widgets[index]['ip_btn'].config(text="IP ON", style="Green.TButton")

        # Ping szál indítása
        thread = threading.Thread(target=self.ping_worker, args=(index,))
        thread.daemon = True
        thread.start()
        self.ping_threads[index] = thread

    def stop_ping(self, index):
        if index in self.running_pings:
            self.running_pings[index] = False
            modem = self.modems[index]
            ip_text = "IP OFF" if modem['ip'] else "IP OFF"
            ip_style = "Red.TButton" if modem['ip'] else "Red.TButton"
            self.modem_widgets[index]['ip_btn'].config(text=ip_text, style=ip_style)
            if index in self.ping_threads:
                del self.ping_threads[index]

    def ping_worker(self, index):
        modem = self.modems[index]
        ip_address = modem['ip']

        # Log fájl létrehozása
        current_date = datetime.now().strftime('%Y%m%d')
        log_dir = os.path.join(self.script_dir, current_date)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_file = os.path.join(log_dir, f"ping_{ip_address}_{current_date}.txt")

        last_line_break = time.time()
        char_count = 0  # Karakterszámláló a sorban

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n=== Ping started at {datetime.now().strftime('%Y.%m.%d %H:%M:%S')} ===\n")
            char_count = 0  # Új sor után nullázzuk

            while self.running_pings.get(index, False):
                try:
                    # Console ablak elrejtése
                    SW_HIDE = 0
                    HWND = ctypes.windll.kernel32.GetConsoleWindow()
                    ctypes.windll.user32.ShowWindow(HWND, SW_HIDE)

                    # Ping parancs végrehajtása
                    result = subprocess.run(
                        ["ping", "-n", "1", "-l", str(self.ping_packet_size),
                         "-w", str(self.ping_timeout), ip_address],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        creationflags=subprocess.CREATE_NO_WINDOW
                    )

                    # Console ablak visszaállítása
                    SW_SHOW = 5
                    ctypes.windll.user32.ShowWindow(HWND, SW_SHOW)

                    # Eredmény kiértékelése
                    if "Reply from" in result.stdout:
                        f.write("+")
                    else:
                        f.write("-")
                    f.flush()

                    char_count += 1

                    # 150 karakter ellenőrzése - új sor
                    if char_count >= 150:
                        f.write("\n")
                        char_count = 0

                    # Line break ellenőrzése (időalapú)
                    current_time = time.time()
                    if current_time - last_line_break >= (self.ping_line_break * 60):
                        if char_count > 0:  # Ha nem üres sorban vagyunk
                            f.write("\n")
                        f.write(f"{datetime.now().strftime('%Y.%m.%d %H:%M:%S')}\n")
                        last_line_break = current_time
                        char_count = 0

                except Exception as e:
                    f.write("-")
                    f.flush()
                    char_count += 1

                    # 150 karakter ellenőrzése hiba esetén is
                    if char_count >= 150:
                        f.write("\n")
                        char_count = 0

                time.sleep(self.ping_interval)

    def toggle_debug(self, index):
        modem = self.modems[index]
        if not (modem['port'] and modem['baud_rate'] and modem['data_type']):
            messagebox.showwarning("Figyelem", "Nincs elegendő adat a debug logoláshoz (port, baud rate, data type szükséges).")
            return

        if index in self.running_debugs:
            self.stop_debug(index)
        else:
            self.start_debug(index)

    def start_debug(self, index):
        modem = self.modems[index]
        if not (modem['port'] and modem['baud_rate'] and modem['data_type']):
            return

        self.running_debugs[index] = True
        self.modem_widgets[index]['debug_btn'].config(text="DEBUG ON", style="Green.TButton")

        # Debug szál indítása
        thread = threading.Thread(target=self.debug_worker, args=(index,))
        thread.daemon = True
        thread.start()
        self.debug_threads[index] = thread

    def stop_debug(self, index):
        if index in self.running_debugs:
            self.running_debugs[index] = False
            modem = self.modems[index]
            has_debug_data = bool(modem['port'] and modem['baud_rate'] and modem['data_type'])
            debug_text = "DEBUG OFF" if has_debug_data else "DEBUG OFF"
            debug_style = "Red.TButton" if has_debug_data else "Red.TButton"
            self.modem_widgets[index]['debug_btn'].config(text=debug_text, style=debug_style)
            if index in self.debug_threads:
                del self.debug_threads[index]

    def debug_worker(self, index):
        modem = self.modems[index]
        port = f"COM{modem['port']}" if modem['port'].isdigit() else modem['port']
        baud_rate = int(modem['baud_rate'])

        # Log fájl létrehozása
        current_date = datetime.now().strftime('%Y%m%d')
        log_dir = os.path.join(self.script_dir, current_date)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_file = os.path.join(log_dir, f"debug_{port}_{current_date}.txt")

        retry_count = 0
        max_retries = 60  # 1 perc újrapróbálkozás

        while self.running_debugs.get(index, False):
            try:
                with serial.Serial(port, baud_rate, timeout=1) as ser:
                    retry_count = 0  # Sikeres kapcsolat esetén nullázzuk

                    with open(log_file, 'a', encoding='utf-8') as f:
                        f.write(f"\n=== Debug started at {datetime.now().strftime('%Y.%m.%d %H:%M:%S')} ===\n")

                        while self.running_debugs.get(index, False):
                            if ser.in_waiting > 0:
                                try:
                                    line_bytes = ser.readline()
                                    try:
                                        line = line_bytes.decode('utf-8').strip()
                                    except UnicodeDecodeError:
                                        try:
                                            line = line_bytes.decode('iso-8859-1').strip()
                                        except UnicodeDecodeError:
                                            continue

                                    if line:
                                        timestamp = datetime.now().strftime('%Y.%m.%d %H:%M:%S')
                                        f.write(f"[{timestamp}] {line}\n")
                                        f.flush()

                                except Exception as e:
                                    continue

                            time.sleep(0.02)

            except serial.SerialException as e:
                retry_count += 1
                if retry_count >= max_retries:
                    break
                time.sleep(1)  # 1 másodperc várakozás újrapróbálkozás előtt
            except Exception as e:
                break

    def show_ip_log(self, index):
        modem = self.modems[index]
        if not modem['ip']:
            messagebox.showwarning("Figyelem", "Nincs IP cím megadva ehhez a modemhez.")
            return

        current_date = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(self.script_dir, current_date, f"ping_{modem['ip']}_{current_date}.txt")

        # Élő log ablak megnyitása
        self.open_live_log_window(modem['ip'], log_file, "IP")

    def show_debug_log(self, index):
        modem = self.modems[index]
        if not modem['port']:
            messagebox.showwarning("Figyelem", "Nincs port megadva ehhez a modemhez.")
            return

        port = f"COM{modem['port']}" if modem['port'].isdigit() else modem['port']
        current_date = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(self.script_dir, current_date, f"debug_{port}_{current_date}.txt")

        # Élő log ablak megnyitása
        self.open_live_log_window(port, log_file, "DEBUG")

    def on_exit(self):
        # Minden szál leállítása
        for index in list(self.running_pings.keys()):
            self.stop_ping(index)
        for index in list(self.running_debugs.keys()):
            self.stop_debug(index)

        self.root.quit()
        self.root.destroy()

    def open_live_log_window(self, identifier, log_file, log_type):
        """Élő log ablak megnyitása"""
        # Ellenőrizzük, hogy már van-e nyitott ablak ehhez a loghoz
        window_key = f"{log_type}_{identifier}"
        if hasattr(self, 'log_windows') and window_key in self.log_windows:
            # Ha már van ablak, előtérbe hozzuk
            try:
                self.log_windows[window_key].lift()
                return
            except:
                # Ha az ablak már bezárult, töröljük a referenciát
                del self.log_windows[window_key]

        if not hasattr(self, 'log_windows'):
            self.log_windows = {}

        # Új ablak létrehozása
        log_window = tk.Toplevel(self.root)
        log_window.title(f"{log_type} Log - {identifier}")
        log_window.geometry("800x600")
        log_window.minsize(400, 300)

        # Listbox a log tartalmához
        log_listbox = tk.Listbox(log_window, font=("Courier", 10))
        log_listbox.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        # Scrollbar
        scrollbar = ttk.Scrollbar(log_window, orient="vertical", command=log_listbox.yview)
        log_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="right", fill="y")

        # Auto-scroll változó
        auto_scroll = [True]  # Lista, hogy referencia szerint módosítható legyen

        def on_mouse_scroll(event):
            # Ha a lista alján vagyunk, auto-scroll be
            if log_listbox.yview()[1] == 1.0:
                auto_scroll[0] = True
            else:
                auto_scroll[0] = False

        def on_mouse_middle_click(event):
            # Középső gomb kattintás váltja az auto-scroll-t
            auto_scroll[0] = not auto_scroll[0]

        log_listbox.bind("<MouseWheel>", on_mouse_scroll)
        log_listbox.bind("<Button-2>", on_mouse_middle_click)

        # Log fájl monitorozása
        def monitor_log_file():
            last_size = 0
            while window_key in self.log_windows:
                try:
                    if os.path.exists(log_file):
                        current_size = os.path.getsize(log_file)
                        if current_size != last_size:
                            # Fájl változott, újratöltjük
                            with open(log_file, 'r', encoding='utf-8') as f:
                                content = f.read()

                            # Frissítjük a listbox-ot a fő szálon
                            def update_listbox():
                                if window_key in self.log_windows:
                                    log_listbox.delete(0, tk.END)
                                    lines = content.split('\n')
                                    for line in lines:
                                        if line.strip():
                                            log_listbox.insert(tk.END, line)

                                    if auto_scroll[0]:
                                        log_listbox.see(tk.END)

                            log_window.after(0, update_listbox)
                            last_size = current_size

                    time.sleep(1)  # 1 másodpercenként ellenőrizzük
                except Exception as e:
                    break

        # Monitor szál indítása
        monitor_thread = threading.Thread(target=monitor_log_file)
        monitor_thread.daemon = True
        monitor_thread.start()

        # Ablak bezárás kezelése
        def on_close():
            if window_key in self.log_windows:
                del self.log_windows[window_key]
            log_window.destroy()

        log_window.protocol("WM_DELETE_WINDOW", on_close)

        # Ablak referencia tárolása
        self.log_windows[window_key] = log_window

        # Kezdeti betöltés
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            lines = content.split('\n')
            for line in lines:
                if line.strip():
                    log_listbox.insert(tk.END, line)
            if auto_scroll[0]:
                log_listbox.see(tk.END)

if __name__ == "__main__":
    root = tk.Tk()
    app = M2MORT(root)
    root.mainloop()
